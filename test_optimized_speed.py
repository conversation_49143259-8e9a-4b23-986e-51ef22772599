#!/usr/bin/env python3
"""
测试优化后的Reddit数据获取速度
验证每天3条帖子的目标是否能快速达成
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from reddit_batch_fetcher import RedditBatchFetcher
from datetime import datetime, timedelta
import json


def test_speed_optimization():
    """测试速度优化效果"""
    print("⚡ 测试优化后的Reddit数据获取速度")
    print("=" * 60)
    
    try:
        # 创建获取器实例
        fetcher = RedditBatchFetcher()
        
        print(f"📊 配置信息:")
        print(f"   每日最小帖子数: {fetcher.min_posts_per_day}")
        print(f"   时间范围: {fetcher.start_date.strftime('%Y-%m-%d')} 到 {fetcher.end_date.strftime('%Y-%m-%d')}")
        print(f"   目标股票: {list(fetcher.target_stocks.keys())}")
        
        # 测试最近几天的数据获取速度
        test_dates = [
            datetime(2025, 6, 23),
            datetime(2025, 6, 24),
            datetime(2025, 6, 25)
        ]
        
        ticker = 'AAPL'
        total_start_time = time.time()
        
        print(f"\n🧪 开始速度测试 - {ticker}")
        print("-" * 40)
        
        results = []
        
        for i, test_date in enumerate(test_dates, 1):
            print(f"\n📅 测试 {i}/{len(test_dates)}: {test_date.strftime('%Y-%m-%d')}")
            
            start_time = time.time()
            
            # 获取单日数据
            posts = fetcher.fetch_posts_for_date(ticker, test_date)
            
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                'date': test_date.strftime('%Y-%m-%d'),
                'posts_found': len(posts),
                'duration_seconds': round(duration, 2),
                'target_met': len(posts) >= fetcher.min_posts_per_day,
                'posts_per_second': round(len(posts) / duration, 2) if duration > 0 else 0
            }
            
            results.append(result)
            
            print(f"   ⏱️  耗时: {duration:.2f} 秒")
            print(f"   📊 帖子数: {len(posts)}")
            print(f"   🎯 达标: {'✅' if result['target_met'] else '❌'}")
            print(f"   📈 效率: {result['posts_per_second']} 帖子/秒")
        
        total_end_time = time.time()
        total_duration = total_end_time - total_start_time
        
        # 计算统计信息
        total_posts = sum(r['posts_found'] for r in results)
        avg_duration = sum(r['duration_seconds'] for r in results) / len(results)
        success_rate = sum(1 for r in results if r['target_met']) / len(results) * 100
        
        print(f"\n📊 速度测试总结:")
        print("=" * 40)
        print(f"   总耗时: {total_duration:.2f} 秒")
        print(f"   平均每日耗时: {avg_duration:.2f} 秒")
        print(f"   总帖子数: {total_posts}")
        print(f"   达标率: {success_rate:.1f}%")
        print(f"   整体效率: {total_posts / total_duration:.2f} 帖子/秒")
        
        # 预估完整执行时间
        total_days = (fetcher.end_date - fetcher.start_date).days + 1
        estimated_time_per_stock = avg_duration * total_days
        estimated_total_time = estimated_time_per_stock * len(fetcher.target_stocks)
        
        print(f"\n⏰ 完整执行时间预估:")
        print(f"   总天数: {total_days} 天")
        print(f"   每股票预估时间: {estimated_time_per_stock / 60:.1f} 分钟")
        print(f"   全部股票预估时间: {estimated_total_time / 60:.1f} 分钟 ({estimated_total_time / 3600:.1f} 小时)")
        
        # 性能评估
        if avg_duration < 30:
            performance = "🚀 优秀"
        elif avg_duration < 60:
            performance = "✅ 良好"
        elif avg_duration < 120:
            performance = "⚠️ 一般"
        else:
            performance = "❌ 较慢"
        
        print(f"   性能评级: {performance}")
        
        # 保存测试结果
        test_report = {
            'test_time': datetime.now().isoformat(),
            'configuration': {
                'min_posts_per_day': fetcher.min_posts_per_day,
                'date_range': f"{fetcher.start_date.strftime('%Y-%m-%d')} to {fetcher.end_date.strftime('%Y-%m-%d')}",
                'total_days': total_days,
                'target_stocks': list(fetcher.target_stocks.keys())
            },
            'test_results': results,
            'summary': {
                'total_duration': round(total_duration, 2),
                'average_duration_per_day': round(avg_duration, 2),
                'total_posts_found': total_posts,
                'success_rate_percent': round(success_rate, 1),
                'overall_efficiency': round(total_posts / total_duration, 2)
            },
            'estimates': {
                'estimated_time_per_stock_minutes': round(estimated_time_per_stock / 60, 1),
                'estimated_total_time_hours': round(estimated_total_time / 3600, 1),
                'performance_rating': performance
            }
        }
        
        with open('speed_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(test_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存: speed_test_report.json")
        
        # 给出建议
        print(f"\n💡 优化建议:")
        if success_rate == 100:
            print("   ✅ 所有测试都达标，配置合理")
        elif success_rate >= 80:
            print("   ⚠️ 大部分测试达标，可以接受")
        else:
            print("   ❌ 达标率较低，可能需要进一步调整")
        
        if estimated_total_time < 3600:  # 1小时
            print("   ⚡ 预估执行时间合理，可以开始批量获取")
        elif estimated_total_time < 7200:  # 2小时
            print("   ⏰ 预估执行时间较长，建议分批执行")
        else:
            print("   🐌 预估执行时间很长，建议进一步优化或分多次执行")
        
        return test_report
        
    except Exception as e:
        print(f"\n❌ 速度测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    test_speed_optimization()
