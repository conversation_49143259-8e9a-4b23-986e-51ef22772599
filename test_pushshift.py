import requests
import os
import json
from datetime import datetime, timedelta
import time

# 股票关键词
stock_keywords = ['AAPL', 'MSFT', 'NVDA']
start_date = datetime.strptime("2025-01-01", "%Y-%m-%d")
end_date = datetime.strptime("2025-06-25", "%Y-%m-%d")

# 创建对应文件夹
for stock in stock_keywords:
    os.makedirs(f"{stock}_Reddit", exist_ok=True)

# 获取某天关键词的帖子
def fetch_reddit_posts(keyword, after, before, size=100):
    url = "https://api.pushshift.io/reddit/search/submission/"
    params = {
        "q": keyword,
        "after": after,
        "before": before,
        "size": size,
        "sort": "desc"
    }
    try:
        response = requests.get(url, params=params)
        if response.status_code == 200:
            return response.json().get("data", [])
        else:
            print(f"请求失败 {response.status_code}")
            return []
    except Exception as e:
        print(f"网络错误：{e}")
        return []

# 日期迭代
current_date = start_date
while current_date <= end_date:
    next_date = current_date + timedelta(days=1)
    after_ts = int(current_date.timestamp())
    before_ts = int(next_date.timestamp())
    date_str = current_date.strftime("%Y-%m-%d")

    for stock in stock_keywords:
        posts = fetch_reddit_posts(stock, after_ts, before_ts)

        # 保证至少有3条（若不足，仍保存已有数据）
        if len(posts) < 3:
            print(f"[{date_str}] {stock} 仅有 {len(posts)} 条帖子")

        daily_data = []
        for post in posts[:max(3, len(posts))]:  # 最少3条
            daily_data.append({
                "title": post.get("title", ""),
                "selftext": post.get("selftext", ""),
                "url": post.get("url", ""),
                "created_utc": post.get("created_utc", 0),
                "subreddit": post.get("subreddit", ""),
                "score": post.get("score", 0)
            })

        # 保存为 json 文件
        output_path = f"{stock}_Reddit/{date_str}.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(daily_data, f, ensure_ascii=False, indent=2)

        time.sleep(1.2)  # 控制请求频率，避免限流

    current_date += timedelta(days=1)
