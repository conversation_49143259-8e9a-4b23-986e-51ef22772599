#!/usr/bin/env python3
"""
Reddit数据获取测试脚本
用于测试当天是否能获取到AAPL的Reddit帖子数据
"""

import sys
import os
from datetime import datetime, timedelta
from reddit_batch_fetcher import RedditBatchFetcher

def test_single_day_fetch():
    """测试单日数据获取"""
    print("🧪 测试单日Reddit数据获取")
    print("=" * 50)
    
    try:
        # 创建获取器实例
        fetcher = RedditBatchFetcher()
        
        # 测试参数
        ticker = 'AAPL'
        test_date = datetime(2025, 6, 25)  # 今天
        
        print(f"📅 测试日期: {test_date.strftime('%Y-%m-%d')}")
        print(f"🎯 测试股票: {ticker}")
        print(f"📝 目标帖子数: {fetcher.min_posts_per_day}")
        
        # 创建文件夹
        fetcher.create_folder_structure()
        
        # 获取帖子数据
        print(f"\n🔍 开始获取 {ticker} 的帖子数据...")
        posts = fetcher.fetch_posts_for_date(ticker, test_date)
        
        print(f"\n📊 获取结果:")
        print(f"   找到帖子数: {len(posts)}")
        print(f"   目标达成: {'✅' if len(posts) >= fetcher.min_posts_per_day else '❌'}")
        
        if posts:
            print(f"\n📝 帖子详情:")
            for i, post in enumerate(posts[:5], 1):  # 只显示前5个
                print(f"   {i}. 【{post['subreddit']}】{post['title'][:60]}...")
                print(f"      得分: {post['score']} | 评论: {post['num_comments']} | 情绪: {post['sentiment']['label']}")
                print(f"      日期: {post['created_date']}")
                print()
        
        # 如果帖子不足，尝试扩展搜索
        if len(posts) < fetcher.min_posts_per_day:
            print(f"⚠️ 帖子数量不足，尝试扩展搜索...")
            additional_posts = fetcher.fetch_additional_posts(ticker, test_date, fetcher.min_posts_per_day - len(posts))
            
            if additional_posts:
                print(f"🔍 扩展搜索找到 {len(additional_posts)} 个额外帖子")
                posts.extend(additional_posts)
                
                # 去重
                seen_ids = set()
                unique_posts = []
                for post in posts:
                    if post['id'] not in seen_ids:
                        seen_ids.add(post['id'])
                        unique_posts.append(post)
                posts = unique_posts
                
                print(f"📊 去重后总帖子数: {len(posts)}")
        
        # 保存数据
        if posts:
            print(f"\n💾 保存数据...")
            save_data = fetcher.save_daily_posts(ticker, test_date, posts)
            print(f"✅ 数据已保存到: {fetcher.target_stocks[ticker]['folder']}")
            
            # 显示统计信息
            stats = save_data['statistics']
            print(f"\n📈 统计信息:")
            print(f"   总帖子数: {save_data['total_posts']}")
            print(f"   平均情绪得分: {stats['average_sentiment_score']}")
            print(f"   平均Reddit得分: {stats['average_reddit_score']}")
            print(f"   情绪分布: 正面({stats['sentiment_distribution']['positive']}) "
                  f"负面({stats['sentiment_distribution']['negative']}) "
                  f"中性({stats['sentiment_distribution']['neutral']})")
            print(f"   主要subreddit: {list(stats['subreddit_distribution'].keys())[:3]}")
        else:
            print("❌ 未找到任何相关帖子")
            
            # 提供诊断信息
            print("\n🔧 可能的解决方案:")
            print("   1. 检查Reddit API连接状态")
            print("   2. 尝试不同的搜索关键词")
            print("   3. 扩大搜索时间范围")
            print("   4. 检查subreddit访问权限")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_multiple_dates():
    """测试多个日期的数据获取"""
    print("\n🧪 测试多日期数据获取")
    print("=" * 50)
    
    try:
        fetcher = RedditBatchFetcher()
        ticker = 'AAPL'
        
        # 测试最近3天
        test_dates = [
            datetime(2025, 6, 25),
            datetime(2025, 6, 24),
            datetime(2025, 6, 23)
        ]
        
        results = []
        
        for test_date in test_dates:
            print(f"\n📅 测试日期: {test_date.strftime('%Y-%m-%d')}")
            posts = fetcher.fetch_posts_for_date(ticker, test_date)
            
            result = {
                'date': test_date.strftime('%Y-%m-%d'),
                'posts_count': len(posts),
                'target_met': len(posts) >= fetcher.min_posts_per_day
            }
            results.append(result)
            
            print(f"   找到帖子: {len(posts)} 个 {'✅' if result['target_met'] else '❌'}")
        
        print(f"\n📊 多日期测试总结:")
        total_days = len(results)
        successful_days = sum(1 for r in results if r['target_met'])
        
        print(f"   测试天数: {total_days}")
        print(f"   成功天数: {successful_days}")
        print(f"   成功率: {successful_days/total_days*100:.1f}%")
        
        for result in results:
            status = "✅" if result['target_met'] else "❌"
            print(f"   {result['date']}: {result['posts_count']} 个帖子 {status}")
    
    except Exception as e:
        print(f"❌ 多日期测试失败: {e}")

def main():
    """主函数"""
    print("🚀 Reddit数据获取测试工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--multi":
            test_multiple_dates()
        elif sys.argv[1] == "--single":
            test_single_day_fetch()
        else:
            print("用法:")
            print("  python test_reddit_fetch.py --single   # 测试单日获取")
            print("  python test_reddit_fetch.py --multi    # 测试多日获取")
    else:
        # 默认运行单日测试
        test_single_day_fetch()

if __name__ == "__main__":
    main()
